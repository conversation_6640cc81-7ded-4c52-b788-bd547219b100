<template>
  <section>
    <a-card size="small" title="进口计划信息" class="cs-card-form">
      <div class="cs-form">
        <a-form ref="formRef" labelAlign="right" :label-col="{ style: { width: '140px' } }" :rules="rules"
                :model="formData" class="grid-container">
          <!-- 业务类型 -->
          <a-form-item name="businessType" :label="'业务类型'" class="grid-item" :colon="false">
            <cs-select disabled optionFilterProp="label" option-label-prop="key" allow-clear show-search
                       v-model:value="formData.businessType" id="businessType">
              <a-select-option v-for="item in businessTypes" :key="item.value + ' ' + item.label"
                               :value="item.value" :label="item.value + item.label">
                {{ item.value }} {{ item.label }}
              </a-select-option>
            </cs-select>
          </a-form-item>
          <!-- 计划编号 -->
          <a-form-item name="planId" :label="'计划编号'" class="grid-item" :colon="false">
            <a-input :disabled="fieldDisabled" size="small" v-model:value="formData.planId"/>
          </a-form-item>
          <!-- 计划年度 -->
          <a-form-item name="planYear" :label="'计划年度'" class="grid-item" :colon="false">
            <a-date-picker
              :disabled="fieldDisabled"
              v-model:value="formData.planYear"
              id="planYear"
              valueFormat="YYYY-MM-DD HH:mm:ss"
              format="YYYY"
              :locale="locale"
              picker="year"
              size="small"
              style="width: 100%"
              placeholder=""
            />
          </a-form-item>
          <!-- 上下半年 -->
          <a-form-item name="halfYear" :label="'上下半年'" class="grid-item" :colon="false">
            <cs-select :disabled="fieldDisabled" optionFilterProp="label" option-label-prop="key" allow-clear show-search
                       v-model:value="formData.halfYear" id="halfYear">
              <a-select-option v-for="item in halfYearOptions" :key="item.value + ' ' + item.label"
                               :value="item.value" :label="item.value + item.label">
                {{ item.value }} {{ item.label }}
              </a-select-option>
            </cs-select>
          </a-form-item>
          <!-- 备注 -->
          <a-form-item name="remark" :label="'备注'" class="grid-item merge-3" :colon="false">
            <a-textarea :disabled="fieldDisabled" size="small" v-model:value="formData.remark" :autosize="{ minRows: 3, maxRows: 10 }"/>
          </a-form-item>
          <!-- 制单人 -->
          <a-form-item name="createrUser" :label="'制单人'" class="grid-item" :colon="false">
            <a-input disabled size="small" v-model:value="formData.createrUser"/>
          </a-form-item>
          <!-- 制单时间 -->
          <a-form-item name="createrTime" :label="'制单时间'" class="grid-item" :colon="false">
            <a-date-picker
              disabled
              v-model:value="formData.createrTime"
              id="createrTime"
              valueFormat="YYYY-MM-DD HH:mm:ss"
              format="YYYY-MM-DD HH:mm:ss"
              :locale="locale"
              placeholder=""
              size="small"
              style="width: 100%"
            />
          </a-form-item>
          <!-- 单据状态 -->
          <a-form-item name="status" :label="'单据状态'" class="grid-item" :colon="false">
            <cs-select disabled optionFilterProp="label" option-label-prop="key" allow-clear show-search
                       v-model:value="formData.status" id="status">
              <a-select-option v-for="item in productClassify.data_status" :key="item.value + ' ' + item.label"
                               :value="item.value" :label="item.value + item.label">
                {{ item.value }} {{ item.label }}
              </a-select-option>
            </cs-select>
          </a-form-item>
          <!-- 确认时间 -->
          <a-form-item name="confirmTime" :label="'确认时间'" class="grid-item" :colon="false">
            <a-date-picker
              disabled
              v-model:value="formData.confirmTime"
              id="confirmTime"
              valueFormat="YYYY-MM-DD HH:mm:ss"
              format="YYYY-MM-DD HH:mm:ss"
              :locale="locale"
              size="small"
              style="width: 100%"
              placeholder=""
              showTime
            />
          </a-form-item>
          <!-- 审批状态 -->
          <a-form-item name="apprStatus" :label="'审批状态'" class="grid-item" :colon="false">
            <cs-select disabled optionFilterProp="label" option-label-prop="key" allow-clear show-search
                       v-model:value="formData.apprStatus" id="apprStatus">
              <a-select-option v-for="item in productClassify.approval_status" :key="item.value + ' ' + item.label"
                               :value="item.value" :label="item.value + item.label">
                {{ item.value }} {{ item.label }}
              </a-select-option>
            </cs-select>
          </a-form-item>
          <!-- 版本号 -->
          <a-form-item name="versionNo" :label="'版本号'" class="grid-item" :colon="false">
            <a-input disabled size="small" v-model:value="formData.versionNo"/>
          </a-form-item>

          <div class="cs-submit-btn merge-3">
            <a-button size="small" type="primary" @click="handlerSave" class="cs-margin-right"
                      v-show="props.editConfig.editStatus !== editStatus.SHOW" :disabled="fieldDisabled">保存
            </a-button>
            <!--            <a-button size="small" type="primary" @click="handlerSaveClose" class="cs-margin-right"
                                  v-show="props.editConfig.editStatus !== editStatus.SHOW">保存关闭
                        </a-button>-->
            <a-button size="small" class="cs-margin-right cs-warning" @click="onBack(true)">返回</a-button>
          </div>
        </a-form>
      </div>
    </a-card>
    <a-card size="small"  class="cs-card-form">
      <plan-detail-table
        ref="detailTableRef"
        :disabled="showDisable"
        :head-id="formData.sid"
        :edit-config="props.editConfig"
        @change="handleDetailChange"
      />
    </a-card>
  </section>
</template>

<script setup>
import {editStatus, productClassify} from '@/view/common/constant'
import {message, Modal} from "ant-design-vue";
import {onMounted, reactive, ref, computed} from "vue";
import CsSelect from "@/components/select/CsSelect.vue";
import {usePCode} from "@/view/common/usePCode";
import zhCN from 'ant-design-vue/es/date-picker/locale/zh_CN';
import {
  insertPlan,
  updatePlan,
  // savePlanWithDetails,
  updatePlanWithDetails,
  insertPlanWithDetails
} from "@/api/importedCigarettes/plan/planApi";
import PlanDetailTable from './list/PlanDetailTable.vue';
import {getUserInfo} from "@/api/payment/payment_info";

const { getPCode } = usePCode()

const props = defineProps({
  editConfig: {
    type: Object,
    default: () => {
    }
  }
});

// 定义子组件 emit事件，用于子组件向父组件传递数据
const emit = defineEmits(['onEditBack','editShowBody']);

const onBack = (val) => {
  emit('onEditBack', val);
};

const onBackShowBody = (val,editStatus,headId) => {
  emit('editShowBody', val, editStatus, headId);
};

// 是否禁用
const showDisable = ref(false)
// 添加表头控制字段
const hasHeadControl = computed(() => {
  return (formData.hasHeadCtr === '1' || formData.isCopy ==='1')
})

// 计算最终禁用状态（结合显示模式和表头控制）
const fieldDisabled = computed(() => {
  return showDisable.value || hasHeadControl.value
})

const businessTypes = reactive([
  {
    label:'国营贸易进口卷烟',
    value:'1'
  },
  {
    label:'国营贸易进口辅料',
    value:'2'
  },
])

const halfYearOptions = reactive([
  {
    label:'上半年',
    value:'0'
  },
  {
    label:'下半年',
    value:'1'
  },
],)

const portOptions = reactive([
  {
    label:'起运港',
    value:'0'
  },
  {
    label:'目的港',
    value:'1'
  },
])

// 表单数据
const formData = reactive({
  businessType: '1',
  planId: '',
  planYear: '',
  halfYear: '',
  remark: '',
  createrUser: '',
  createrTime: '',
  status: '0',
  confirmTime: '',
  apprStatus: '0',
  versionNo: '1',
  sid:  '',
})

// 表单校验规则
const rules = {
  // businessType: [
  //   {required: true, message: '请选择业务类型', trigger: 'change'},
  //   {max: 60, message: '业务类型不能超过60个字符', trigger: 'blur'}
  // ],
  planId: [
    {required: true, message: '请输入计划编号', trigger: 'blur'},
    {max: 60, message: '计划编号不能超过60个字符', trigger: 'blur'}
  ],
  planYear: [
    {required: true, message: '请选择计划年度', trigger: 'change'},
  ],
  halfYear: [
    {required: true, message: '请选择上下半年', trigger: 'change'},
    {max: 10, message: '上下半年不能超过10个字符', trigger: 'blur'}
  ],
  createrUser: [
    {required: true, message: '请输入制单人', trigger: 'blur'},
  ],
  createrTime: [
    {required: true, message: '请选择制单时间', trigger: 'change'}
  ],
  // status: [
  //   {required: true, message: '请选择单据状态', trigger: 'change'},
  //   {max: 10, message: '单据状态不能超过10个字符', trigger: 'blur'}
  // ],
  versionNo: [
    {required: true, message: '请输入版本号', trigger: 'blur'},
  ]
}

/*const locale = {
  lang: {
    locale: 'zh_CN',
    placeholder: 'Select date',
    rangePlaceholder: ['Start date', 'End date'],
    today: 'Today',
    now: 'Now',
    backToToday: 'Back to today',
    ok: 'Ok',
    clear: 'Clear',
    month: 'Month',
    year: 'Year',
    timeSelect: 'Select time',
    dateSelect: 'Select date',
    monthSelect: 'Choose a month',
    yearSelect: 'Choose a year',
    decadeSelect: 'Choose a decade',
    yearFormat: 'YYYY',
    dateFormat: 'M/D/YYYY',
    dayFormat: 'D',
    dateTimeFormat: 'M/D/YYYY HH:mm:ss',
    monthFormat: 'MMMM',
    monthBeforeYear: true,
    previousMonth: 'Previous month (PageUp)',
    nextMonth: 'Next month (PageDown)',
    previousYear: 'Last year (Control + left)',
    nextYear: 'Next year (Control + right)',
    previousDecade: 'Last decade',
    remark: 'remark',
    nextDecade: 'Next decade',
    previousCentury: 'Last century',
    nextCentury: 'Next century',
  },
}*/

// 表单引用
const formRef = ref()

// 表格引用
const detailTableRef = ref();

// 处理明细数据变化
const handleDetailChange = (details) => {
  formData.details = details;
};

// 修改保存处理函数
const handlerSave = async () => {
  try {
    // 先验证表头数据
    await formRef.value.validate();

    // 获取表体数据
    const details = detailTableRef.value.getTableData();

    // 构建完整的保存数据
    const saveData = {
      ...formData,
      details: details,
      sid: formData.sid // 确保 sid 被正确传递
    };

    // 根据编辑状态判断是新增还是修改
    if (props.editConfig.editStatus === editStatus.ADD) {
      const res = await insertPlanWithDetails(saveData);
      if (res.success) {
        // 更新表单数据为后端返回的数据
        Object.assign(formData, res.data.head);
        // 更新表体数据
        if (res.data.details) {
          detailTableRef.value.reloadData();
        }
        message.success('保存成功')
        props.editConfig.editStatus = editStatus.EDIT
        props.editConfig.hasSaved = true;  // 设置保存状态
        onBackShowBody(true,editStatus.EDIT,formData.sid);
      } else {
        message.error(res.message || '保存失败');
        // 确保在保存失败时不设置hasSaved标志
        props.editConfig.hasSaved = false;
      }
    } else if (props.editConfig.editStatus === editStatus.EDIT) {
      const res = await updatePlanWithDetails(saveData);
      if (res.success) {
        // 更新表单数据为后端返回的数据
        Object.assign(formData, res.data.head);
        // 更新表体数据
        if (res.data.details) {
          detailTableRef.value.reloadData();
        }
        message.success('保存成功!')
        props.editConfig.hasSaved = true;  // 设置保存状态
      } else {
        message.error(res.message || '保存失败');
        // 确保在保存失败时不设置hasSaved标志
        props.editConfig.hasSaved = false;
      }
    }
  } catch (error) {
    console.error('保存失败:', error);
    // 如果是表单验证错误，不显示错误消息，因为表单验证会自动显示错误
    if (!error.errorFields) {
      message.error('保存失败，请检查必填项');
    }
    // 确保在保存失败时不设置hasSaved标志
    props.editConfig.hasSaved = false;
  }
}

// 修改保存并关闭处理函数
const handlerSaveClose = async () => {
  try {
    // 先验证表头数据
    await formRef.value.validate();

    // 获取表体数据
    const details = detailTableRef.value.getTableData();

    // 构建完整的保存数据
    const saveData = {
      ...formData,
      details: details
    };

    // 根据编辑状态判断是新增还是修改
    if (props.editConfig.editStatus === editStatus.ADD) {
      const res = await insertPlanWithDetails(saveData);
      if (res.success) {
        // 更新表单数据为后端返回的数据
        Object.assign(formData, res.data);
        message.success('保存成功')
        props.editConfig.hasSaved = true;  // 设置保存状态
        onBack(true)
      } else {
        message.error(res.message || '保存失败');
        // 确保在保存失败时不设置hasSaved标志
        props.editConfig.hasSaved = false;
      }
    } else if (props.editConfig.editStatus === editStatus.EDIT) {
      const res = await updatePlanWithDetails(saveData);
      if (res.success) {
        // 更新表单数据为后端返回的数据
        Object.assign(formData, res.data);
        message.success('保存成功!')
        props.editConfig.hasSaved = true;  // 设置保存状态
        onBack(true)
      } else {
        message.error(res.message || '保存失败');
        // 确保在保存失败时不设置hasSaved标志
        props.editConfig.hasSaved = false;
      }
    }
  } catch (error) {
    console.error('保存失败:', error);
    // 如果是表单验证错误，不显示错误消息，因为表单验证会自动显示错误
    if (!error.errorFields) {
      message.error('保存失败，请检查必填项');
    }
    // 确保在保存失败时不设置hasSaved标志
    props.editConfig.hasSaved = false;
  }
}

const getUpdateUser = () => {
  getUserInfo().then((res)=>{
    if (res.data !=null&&formData.createrUser===''){
      formData.createrUser =  res.data.userName
    }
  })
}


const pCode = ref('')

// 组件挂载时根据编辑状态设置表单数据和禁用状态
onMounted(() => {
  getPCode().then(res=>{
    pCode.value = res;
  })
  if (props.editConfig && props.editConfig.editStatus === editStatus.ADD) {
    showDisable.value = false
    // 确保从 editConfig 中获取 sid
    if (props.editConfig.editData && props.editConfig.editData.sid) {
      formData.sid = props.editConfig.editData.sid;
    }
    Object.assign(formData, props.editConfig.editData || {});
  }
  // 初始化数据
  if (props.editConfig && props.editConfig.editStatus === editStatus.EDIT) {
    Object.assign(formData, props.editConfig.editData);
    showDisable.value = false
  }
  if (props.editConfig && props.editConfig.editStatus === editStatus.SHOW) {
    Object.assign(formData, props.editConfig.editData);
    showDisable.value = true
  }
  if (formData.createrTime === '') {
    const now = new Date();
    // 年-月-日
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0'); // 月份从 0 开始，需加1
    const day = String(now.getDate()).padStart(2, '0');
    // 时:分:秒
    const hours = String(now.getHours()).padStart(2, '0');
    const minutes = String(now.getMinutes()).padStart(2, '0');
    const seconds = String(now.getSeconds()).padStart(2, '0');
    // 组合成目标格式
    formData.createrTime = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
  }
  getUpdateUser()
})
</script>

import { h, reactive, ref } from 'vue'
import { Tag, Button, message } from 'ant-design-vue'
import {baseColumns} from "@/view/common/baseColumns";
import { productClassify } from '@/view/common/constant'
import {useColumnsRender} from "../../common/useColumnsRender";
import { GlobalIcon } from '@/components/icon/index.js'
const {baseColumnsExport, baseColumnsShow} = baseColumns()
const { cmbShowRender } = useColumnsRender()

/**
 * 表格点击编辑/查看按钮
 */
function operationEdit(type) {
  return type === 'edit' && !operationEditShow()
    ? {display: 'none'}
    : {marginLeft: type === 'view' && operationEditShow() ? '15px' : '0'}
}


/**
 * 是否显示编辑标签
 * @returns {boolean}
 */
function operationEditShow() {
  // 根据实际情况返回是否显示编辑按钮的状态
  return true // 示例中总是返回 true
}



function getColumns() {
  const commColumns = reactive([
    'businessType', // 业务类型
    'planId',      // 计划编号
    'planYear',    // 计划年度
    'halfYear',    // 上下半年
    'remark',    // 备注
    'insertUser',  // 制单人
    'insertTime',  // 制单时间
    'status',      // 单据状态
    'confirmTime', // 确认时间
    'apprStatus',  // 审批状态
    'versionNo'    // 版本号
  ])

  // 导出字段设置
  const excelColumnsConfig = ref([
    ...baseColumnsExport,
    ...commColumns
  ])

  // table表格字段设置
  const columnsConfig = ref([
    ...baseColumnsShow,
    ...commColumns
  ])

  // table表格字段设置
  const totalColumns = ref([
    {
      title: '操作',
      maxWidth: 80,
      width: 80,
      dataIndex: 'operation',
      key: 'operation',
      align: 'center',
      fixed: 'left',
      resizable:"true",
     /* customRender: ({ record }) => {
        // 根据状态控制按钮禁用
        const isConfirmed = record.status === '1';
        return {
          children: h('div', {
            class: 'operation-container'
          }, [
            h(Button, {
              size: 'small',
              type: 'link',
              onClick: (event) => {
                event.stopPropagation(); // 阻止事件冒泡
                if (isConfirmed) {
                  message.warning('确认状态的数据不允许编辑'); // 弹出警告
                  return; // 阻止进入编辑页面
                }
                handleEditByRow(record); // 进入编辑页面
              },
              style: operationEdit('edit', record) // 传递 record
            }, {
              icon: () => h(GlobalIcon, {
                type: 'form',
                style: { color: isConfirmed ? '#999' : '#e93f41' }
              })
            })
          ])
        }
      }*/
    },
    {
      title: '业务类型',
      width: 135,
      minWidth: 135,
      align: 'center',
      dataIndex: 'businessType',
      key: 'businessType',
      resizable:"true",
      customRender: ({ text }) => {
        return h(<div></div>, cmbShowRender(text,productClassify.businessType))
      }
    },
    {
      title: '计划编号',
      width: 100,
      minWidth: 100,
      align: 'center',
      dataIndex: 'planId',
      resizable:"true",
      key: 'planId'
    },
    {
      title: '计划年度',
      width: 100,
      minWidth: 100,
      align: 'center',
      dataIndex: 'planYear',
      key: 'planYear',
      resizable:"true",
      customRender: ({ text }) => {
        // 如果有值且包含年份，则只截取年份部分
        if (text && typeof text === 'string') {
          // 尝试提取年份（假设格式为yyyy或yyyy-mm-dd等包含年份的格式）
          const yearMatch = text.match(/^(\d{4})/);
          return yearMatch ? yearMatch[1] : text;
        }
        return text;
      }
    },
    {
      title: '上下半年',
      width: 100,
      minWidth: 100,
      align: 'center',
      dataIndex: 'halfYear',
      key: 'halfYear',
      resizable:"true",
      customRender: ({ text }) => {
        const tagColor = '';
        return h(Tag, { color: tagColor }, cmbShowRender(text, productClassify.yearConst))
      }
    },
    {
      title: '制单人',
      width: 100,
      minWidth: 100,
      align: 'center',
      dataIndex: 'createrUser',
      resizable:"true",
      key: 'createrUser'
    },
    {
      title: '制单时间',
      width: 135,
      minWidth: 135,
      align: 'center',
      dataIndex: 'createrTime',
      resizable:"true",
      key: 'createrTime'
    },
    {
      title: '单据状态',
      width: 80,
      minWidth: 80,
      align: 'center',
      dataIndex: 'status',
      resizable:"true",
      key: 'status',
      customRender: ({ text }) => {
        const tagColor = text === '2' ? 'error' : 'success';
        return h(Tag, { color: tagColor }, cmbShowRender(text, productClassify.data_status))
      }
    },
    {
      title: '确认时间',
      width: 135,
      minWidth: 135,
      align: 'center',
      dataIndex: 'confirmTime',
      resizable:"true",
      key: 'confirmTime'
    },
    {
      title: '审批状态',
      width: 100,
      minWidth: 100,
      align: 'center',
      dataIndex: 'apprStatus',
      resizable:"true",
      key: 'apprStatus',
      customRender: ({ text }) => {
        return h(<div></div>, cmbShowRender(text, productClassify.approval_status))
      }
    },
    {
      title: '版本号',
      width: 100,
      minWidth: 100,
      align: 'center',
      dataIndex: 'versionNo',
      resizable:"true",
      key: 'versionNo'
    },
    {
      title: '备注',
      width: 100,
      minWidth: 100,
      align: 'center',
      dataIndex: 'remark',
      resizable:"true",
      key: 'remark'
    },
  ])
  return {
    columnsConfig,
    excelColumnsConfig,
    totalColumns
  }
}

export { getColumns }

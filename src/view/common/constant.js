export const productClassify = {
  commonBusinessType: [
    {
      label: '国营贸易进口卷烟',
      value: '1'
    },
    {
      label: '国营贸易进口辅料',
      value: '2'
    },
    {
      label: '国营贸易进口卷烟设备',
      value: '3'
    },
    {
      label: '国营贸易进口丝束',
      value: '4'
    },
    {
      label: '国营贸易内购内销丝束',
      value: '5'
    },
    {
      label: '非国营贸易进口辅料',
      value: '6'
    }, {
      label: '出料加工进口薄片',
      value: '7'
    },
    {
      label: '出口烟机设备',
      value: '8'
    }, {
      label: '出口辅料',
      value: '9'
    }
  ],

    isAuthorizeList: [
        {
            label: '未授权',
            value: '0'
        },
        {
            label: '已授权',
            value: '1'
        }
    ],
    status:[
        {
            label:'启用',
            value:'0'
        },
        {
            label:'停用',
            value:'1'
        }
    ],
  dataStatus:[
        {
            label:'启用',
            value:'0'
        },
        {
            label:'作废',
            value:'1'
        }
    ],
  nameMethodType:[
    {
      label:'与牌号保持一致',
      value:'0'
    },
    {
      label:'与商标名称保持一致',
      value:'1'
    }
  ],
  //业务类型
    businessType:[
      {
        label:'国营贸易进口卷烟',
        value:'1'
      },
      {
        label:'国营贸易进口辅料',
        value:'2'
      },
      {
        label:'国营贸易进口卷烟设备',
        value:'3'
      },
      {
        label:'国营贸易进口丝束',
        value:'4'
      },
      {
        label:'国营贸易内购内销丝束',
        value:'5'
      },
      {
        label:'非国营贸易进口辅料',
        value:'6'
      },
      {
        label:'出料加工进口薄片',
        value:'7'
      },
      {
        label:'出口烟机设备',
        value:'8'
      },
      {
        label:'出口辅料',
        value:'9'
      }
    ],
  //业务类型
  bizType:[
    {
      label:'国营贸易进口辅料',
      value:'2'
    },
    {
      label:'国营贸易进口卷烟设备',
      value:'3'
    },
    {
      label:'非国营贸易进口辅料',
      value:'6'
    },
  ],
  //国际运输类型
  intlTransType:[
    {
      label:'海运',
      value:'1'
    },
    {
      label:'空运',
      value:'2'
    },
    {
      label:'中欧班列',
      value:'3'
    },
  ],
  //集装箱容量
  containerCap:[
    {
      label:'10吨',
      value:'1'
    },
    {
      label:'20吨',
      value:'2'
    },
  ],
    //单据状态
    state:[
      {
        label:'编制',
        value:'0'
      },
      {
        label:'确认',
        value:'1'
      },
      {
        label:'作废',
        value:'2'
      },
    ],
    isNot:[
      {
        label:'是',
        value:'0'
      },
      {
        label:'否',
        value:'1'
      }
    ],
    isFlag:[
      {
        label:'是',
        value:'1'
      },
      {
        label:'否',
        value:'2'
      }
    ],
    methodAllocation:[
    {
      label:'量',
      value:'0'
    },
    {
      label:'价',
      value:'1'
    }
  ],
  // 海关信用等级
    data_status:[
      {
        label:'编制',
        value:'0'
      },
      {
        label:'确认',
        value:'1'
      },
      {
        label:'作废',
        value:'2'
      }
    ],
    approval_status:[
      {
        label:'不涉及审批',
        value:'0'
      },
      {
        label:'未审批',
        value:'1'
      },
      {
        label:'审批中',
        value:'2'
      },
      {
        label:'审批通过',
        value:'3'
      },
      {
        label:'审批退回',
        value:'4'
      }
    ],
    audit_status:[
      {
        label:'未发送',
        value:'0'
      },
      {
        label:'已发送审核',
        value:'1'
      },
      {
        label:'审核通过',
        value:'2'
      }
    ],
    // 海关信用等级
    creditLevel:[
      {
        label:'高级认证企业',
        value:'1'
      },
      {
        label:'注册等级和备案企业',
        value:'2'
      },
      {
        label:'失信企业',
        value:'4'
      },
    ],
    // 客户类型 供应商 PRD、客户CLI、货代FOD、报关行CUT、企业COM
    customerType:[
      {
        label:'供应商',
        value:'PRD'
      },
      {
        label:'客户',
        value:'CLI'
      },
      {
        label:'货代',
        value:'FOD'
      },
      {
        label:'报关行',
        value:'CUT'
      },
      {
        label:'企业',
        value:'COM'
      }
    ],
  // 业务类型
  // businessType:[
  //   {
  //     label: '国营贸易进口卷烟',
  //     value: '1',
  //   },
  //   {
  //     label: '国营贸易进口辅料',
  //     value: '2',
  //   },
  //   {
  //     label: '国营贸易进口卷烟设备',
  //     value: '3',
  //   },
  //   {
  //     label: '国营贸易进口丝束',
  //     value: '4'
  //   },
  //   {
  //     label: '国营贸易内购内销丝束',
  //     value: '5'
  //   },
  //   {
  //     label: '非国营贸易进口辅料',
  //     value: '6'
  //   },
  //   {
  //     label: '出料加工进口薄片',
  //     value: '7'
  //   },
  //   {
  //     label: '出口烟机设备',
  //     value: '8'
  //   },
  //   {
  //     label: '出口辅料',
  //     value: '9'
  //   }
  // ],
  // 订单单据状态
  // 0编制：未点击订单表头“确认”功能按钮
  // 1确认：点击订单表头“确认”功能按钮，且成功提交
  // 2作废：点击列表“作废”功能按钮，且成功提交
  orderStatus:[
    {
      label: '编制',
      value: '0'
    },
    {
      label: '确认',
      value: '1'
    },
    {
      label: '作废',
      value: '2'
    }
  ],
  // 订单审批状态
  // 0不涉及审批：单据类型未启用审批流程的
  // 1未审批：启用审批流程而未点击列表“发送审批”功能按钮
  // 2审批中：点击列表“发送审批”功能按钮，且成功提交
  // 3审批通过：数据审批通过的（最后一节审批或全部审批通过时）
  // 4审批退回：数据审批某一节点有退回时
  // 不允许修改，置灰
  orderApprStatus:[
    {
      label: '不涉及审批',
      value: '0'
    },
    {
      label: '未审批',
      value: '1'
    },
    {
      label: '审批中',
      value: '2'
    },
    {
      label: '审批通过',
      value: '3'
    },
    {
      label: '审批退回',
      value: '4'
    }
  ],
  // 业务区分
  businessDistinction:[
    {
      label: '上海业务',
      value: '0'
    },
    {
      label: '外地业务',
      value: '1'
    }
  ],
  yearConst:[
    {
      label: '上半年',
      value: '0'
    },
    {
      label: '下半年',
      value: '1'
    }
  ],
  PricePortOptions: [
    {
      label:'起运港',
      value:'0'
    },
    {
      label:'目的港',
      value:'1'
    },
  ],
  // 付款方式
  paymentMethod:[
    {
      label:'付款交单',
      value:'0'
    },
    {
      label:'即期信用证',
      value:'1'
    },
    {
      label:'电汇',
      value:'2'
    },
    {
      label:'预付款',
      value:'3'
    }
  ],
  // 价格条款默认对应港口
  // 默认为1目的港  0起运港1目的港，允许修改
  priceTermPort:[
    {
      label:'起运港',
      value:'0'
    },
    {
      label:'目的港',
      value:'1'
    }
  ],
  paymentMethodMap:[
    {
      label: '付款交单',
      value: '0'
    },
    {
      label: '即期信用证',
      value: '1'
    },
    {
      label: '电汇',
      value: '2'
    },
    {
      label: '预付款',
      value: '3'
    }
  ],
}



/* 通用编辑属性 */
export const editStatus = {
  // 新增
  ADD: `add`,
  // 修改
  EDIT: `edit`,
  // 只读
  SHOW: `show`
}



/* 单证类型 */
export const docType = {
  /* 订单环节文件 */
  orderAttachType: `ORDER_ATTACH_TYPE`,
  /* 销售环节文件 */
  salesAttachType: `SALES_ATTACH_TYPE`,
  /* 结算环节文件 */
  settlementAttachType: `SETTLEMENT`,
  /* 签约环节文件 */
  signingAttachType: `SIGNING_ATTACH_TYPE`,
}

import {deleteAction, getAction, postAction, putAction} from "@/api/manage";
import CsConstant from "@/api/CsConstant";
import ycCsApi from "@/api/ycCsApi";

// 辅料购销合同信息列表
export const insertBuyContract = (params) => window.majesty.httpUtil.postAction(ycCsApi.auxiliaryMaterials.buyContract.insert, params)
export const updateBuyContract = (sid, params) => window.majesty.httpUtil.putAction(`${ycCsApi.auxiliaryMaterials.buyContract.update}/${sid}`, params)
export const deleteBuyContract = (sids) => window.majesty.httpUtil.deleteAction(`${ycCsApi.auxiliaryMaterials.buyContract.delete}/${sids}`)
export const confirmBuyContract = (sid) => window.majesty.httpUtil.postAction(`${ycCsApi.auxiliaryMaterials.buyContract.confirm}/${sid}`)
export const sendAudit = (sids) => window.majesty.httpUtil.postAction(`${ycCsApi.auxiliaryMaterials.buyContract.sendAudit}/${sids}`)
export const invalidateBuyContract = (sids) => window.majesty.httpUtil.postAction(`${ycCsApi.auxiliaryMaterials.buyContract.invalidate}/${sids}`)
export const checkBuyContractNotCancel = (params) => window.majesty.httpUtil.postAction(ycCsApi.auxiliaryMaterials.buyContract.checkNotCancel, params)
export const copyVersion = (params) => window.majesty.httpUtil.postAction(ycCsApi.auxiliaryMaterials.buyContract.copyVersion, params)
export const insertBuyContractWithDetails = (params) => window.majesty.httpUtil.postAction(ycCsApi.auxiliaryMaterials.buyContract.insertWithDetails, params)
export const updateBuyContractWithDetails = (params) => window.majesty.httpUtil.postAction(ycCsApi.auxiliaryMaterials.buyContract.updateWithDetails, params)
export const validateDetailData = (params) => window.majesty.httpUtil.postAction(ycCsApi.auxiliaryMaterials.buyContract.validateDetailData, params)

// 辅料购销合同明细列表
export const updateBuyContractList = (sid, params) => window.majesty.httpUtil.putAction(`${ycCsApi.auxiliaryMaterials.buyContractList.update}/${sid}`, params)
export const deleteBuyContractList = (sids) => window.majesty.httpUtil.deleteAction(`${ycCsApi.auxiliaryMaterials.buyContractList.delete}/${sids}`)

/* 获取自定义配置信息 */
export const saveCustomWithDataId = (params) => window.majesty.httpUtil.postAction(CsConstant.PREFIX_SYSTEM + '/sysCustom/saveCustomWithDataId', params)
export const getCustomVaueByTypeAndDataId = (params) => window.majesty.httpUtil.getAction(CsConstant.PREFIX_SYSTEM + '/sysCustom/getCustomVaueByTypeAndDataId', params)
